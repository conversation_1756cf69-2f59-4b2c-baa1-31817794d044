<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 资源版本模型
 * 第3A阶段：版本控制系统核心模型
 */
class ResourceVersion extends Model
{
    use SoftDeletes;

    protected $table = 'resource_versions';

    // 状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    protected $fillable = [
        'resource_id',
        'user_id',
        'version_number',
        'version_name',
        'description',
        'status',
        'generation_config',
        'generation_task_id',
        'base_version_id',
        'file_path',
        'file_url',
        'file_size',
        'file_hash',
        'estimated_cost',
        'actual_cost',
        'processing_time_ms',
        'is_current',
        'metadata',
        'completed_at'
    ];

    protected $casts = [
        'generation_config' => 'array',
        'metadata' => 'array',
        'estimated_cost' => 'decimal:4',
        'actual_cost' => 'decimal:4',
        'is_current' => 'boolean',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $dates = [
        'completed_at',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 关联资源
     */
    public function resource()
    {
        return $this->belongsTo(Resource::class);
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联生成任务
     */
    public function generationTask()
    {
        return $this->belongsTo(AiGenerationTask::class, 'generation_task_id');
    }

    /**
     * 关联基础版本
     */
    public function baseVersion()
    {
        return $this->belongsTo(ResourceVersion::class, 'base_version_id');
    }

    /**
     * 关联派生版本
     */
    public function derivedVersions()
    {
        return $this->hasMany(ResourceVersion::class, 'base_version_id');
    }

    /**
     * 作用域：按资源过滤
     */
    public function scopeByResource($query, int $resourceId)
    {
        return $query->where('resource_id', $resourceId);
    }

    /**
     * 作用域：按用户过滤
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按状态过滤
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：当前版本
     */
    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    /**
     * 作用域：已完成的版本
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：最新版本
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * 检查版本是否已完成
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 检查版本是否正在处理
     */
    public function isProcessing(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PROCESSING]);
    }

    /**
     * 检查版本是否失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 检查是否为当前版本
     */
    public function isCurrent(): bool
    {
        return $this->is_current;
    }

    /**
     * 获取文件大小（格式化）
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'N/A';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($this->file_size, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . $units[$pow];
    }

    /**
     * 获取处理时间（格式化）
     */
    public function getFormattedProcessingTimeAttribute(): string
    {
        if (!$this->processing_time_ms) {
            return 'N/A';
        }

        $seconds = $this->processing_time_ms / 1000;
        
        if ($seconds < 60) {
            return round($seconds, 1) . 's';
        } elseif ($seconds < 3600) {
            return round($seconds / 60, 1) . 'm';
        } else {
            return round($seconds / 3600, 1) . 'h';
        }
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusDisplayAttribute(): string
    {
        $statusMap = [
            self::STATUS_PENDING => '等待中',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
            self::STATUS_CANCELLED => '已取消'
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取版本类型
     */
    public function getVersionTypeAttribute(): string
    {
        if (!$this->base_version_id) {
            return 'initial'; // 初始版本
        }

        return 'revision'; // 修订版本
    }

    /**
     * 标记为当前版本
     */
    public function markAsCurrent(): void
    {
        // 先取消同一资源的其他当前版本
        self::where('resource_id', $this->resource_id)
            ->where('id', '!=', $this->id)
            ->update(['is_current' => false]);

        // 设置当前版本
        $this->update(['is_current' => true]);
    }

    /**
     * 标记为完成
     */
    public function markAsCompleted(float $actualCost = null, int $processingTimeMs = null): void
    {
        $updateData = [
            'status' => self::STATUS_COMPLETED,
            'completed_at' => Carbon::now()
        ];

        if ($actualCost !== null) {
            $updateData['actual_cost'] = $actualCost;
        }

        if ($processingTimeMs !== null) {
            $updateData['processing_time_ms'] = $processingTimeMs;
        }

        $this->update($updateData);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'metadata' => array_merge($this->metadata ?? [], [
                'error_message' => $errorMessage,
                'failed_at' => Carbon::now()->format('c')
            ])
        ]);
    }

    /**
     * 更新文件信息
     */
    public function updateFileInfo(string $filePath, string $fileUrl, int $fileSize, string $fileHash = null): void
    {
        $this->update([
            'file_path' => $filePath,
            'file_url' => $fileUrl,
            'file_size' => $fileSize,
            'file_hash' => $fileHash
        ]);
    }

    /**
     * 获取版本变更摘要
     */
    public function getChangesSummary(): array
    {
        if (!$this->base_version_id) {
            return ['type' => 'initial', 'description' => '初始版本'];
        }

        $baseVersion = $this->baseVersion;
        if (!$baseVersion) {
            return ['type' => 'unknown', 'description' => '基础版本不存在'];
        }

        $changes = [];
        
        // 比较生成配置
        $baseConfig = $baseVersion->generation_config ?? [];
        $currentConfig = $this->generation_config ?? [];

        foreach ($currentConfig as $key => $value) {
            if (!isset($baseConfig[$key]) || $baseConfig[$key] !== $value) {
                $changes[] = $key;
            }
        }

        return [
            'type' => 'revision',
            'description' => '基于 ' . $baseVersion->version_number . ' 的修订版本',
            'changed_fields' => $changes
        ];
    }

    /**
     * 获取版本统计信息
     */
    public static function getVersionStats(int $resourceId): array
    {
        $versions = self::where('resource_id', $resourceId)->get();
        
        return [
            'total_versions' => $versions->count(),
            'completed_versions' => $versions->where('status', self::STATUS_COMPLETED)->count(),
            'processing_versions' => $versions->whereIn('status', [self::STATUS_PENDING, self::STATUS_PROCESSING])->count(),
            'failed_versions' => $versions->where('status', self::STATUS_FAILED)->count(),
            'current_version' => $versions->where('is_current', true)->first()?->version_number,
            'latest_version' => $versions->sortByDesc('created_at')->first()?->version_number,
            'total_cost' => $versions->sum('actual_cost') ?: $versions->sum('estimated_cost')
        ];
    }
}
