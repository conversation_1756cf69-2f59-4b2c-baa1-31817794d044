<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\WebSocketSession;
use Carbon\Carbon;
use App\Models\AiGenerationTask;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * WebSocket事件推送服务
 */
class WebSocketEventService
{
    protected $webSocketService;

    // 重试配置
    const MAX_RETRY_ATTEMPTS = 3;
    const RETRY_DELAY_SECONDS = 1;
    const EXPONENTIAL_BACKOFF_MULTIPLIER = 2;

    // 降级处理配置
    const FALLBACK_CACHE_TTL = 300; // 5分钟
    const CIRCUIT_BREAKER_THRESHOLD = 5; // 连续失败5次后开启断路器
    const CIRCUIT_BREAKER_TIMEOUT = 60; // 断路器开启60秒后重试

    public function __construct(WebSocketService $webSocketService)
    {
        $this->webSocketService = $webSocketService;
    }

    /**
     * 推送AI生成进度事件
     */
    public function pushAiGenerationProgress(string $taskId, int $userId, int $progress, string $message = ''): bool
    {
        try {
            // 检查断路器状态
            if ($this->isCircuitBreakerOpen($userId)) {
                Log::warning('WebSocket断路器开启，跳过推送', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'progress' => $progress
                ]);

                // 直接存储到降级缓存
                $eventData = [
                    'task_id' => $taskId,
                    'progress' => $progress,
                    'message' => $message,
                    'timestamp' => Carbon::now()->format('c')
                ];
                $this->storeFallbackMessage($userId, WebSocketSession::EVENT_AI_GENERATION_PROGRESS, $eventData);
                return false;
            }

            // 通过external_task_id查找任务记录
            $task = AiGenerationTask::where('external_task_id', $taskId)->first();
            if (!$task) {
                Log::warning('未找到任务记录', [
                    'task_id' => $taskId,
                    'user_id' => $userId
                ]);
                return false;
            }

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'progress' => $progress,
                'message' => $message,
                'timestamp' => Carbon::now()->format('c')
            ];

            // 使用带重试机制的推送
            $success = $this->pushWithRetry(
                $userId,
                WebSocketSession::EVENT_AI_GENERATION_PROGRESS,
                $eventData,
                "ai_generation_progress:{$taskId}"
            );

            if ($success) {
                Log::info('AI生成进度推送成功', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'progress' => $progress,
                    'message' => $message
                ]);
            }

            return $success;

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
                'progress' => $progress,
                'message' => $message,
            ];

            Log::error('AI生成进度推送异常', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 异常情况下也尝试存储降级消息
            try {
                $eventData = [
                    'task_id' => $taskId,
                    'progress' => $progress,
                    'message' => $message,
                    'timestamp' => Carbon::now()->format('c')
                ];
                $this->storeFallbackMessage($userId, WebSocketSession::EVENT_AI_GENERATION_PROGRESS, $eventData);
            } catch (\Exception $fallbackException) {
                Log::error('降级消息存储也失败', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'error' => $fallbackException->getMessage()
                ]);
            }

            return false;
        }
    }

    /**
     * 推送AI生成完成事件
     */
    public function pushAiGenerationCompleted(string $taskId, int $userId): bool
    {
        try {
            // 检查断路器状态
            if ($this->isCircuitBreakerOpen($userId)) {
                Log::warning('WebSocket断路器开启，跳过推送', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'event_type' => 'ai_generation_completed'
                ]);

                // 直接存储到降级缓存
                $eventData = [
                    'task_id' => $taskId,
                    'status' => 'completed',
                    'timestamp' => Carbon::now()->format('c')
                ];
                $this->storeFallbackMessage($userId, WebSocketSession::EVENT_AI_GENERATION_COMPLETED, $eventData);
                return false;
            }

            // 通过external_task_id查找任务记录
            $task = AiGenerationTask::where('external_task_id', $taskId)->first();
            if (!$task) {
                Log::warning('未找到任务记录', [
                    'task_id' => $taskId,
                    'user_id' => $userId
                ]);
                return false;
            }

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'status' => $task->status,
                'output_data' => $task->output_data,
                'cost' => $task->cost,
                'tokens_used' => $task->tokens_used,
                'processing_time_ms' => $task->processing_time_ms,
                'completed_at' => $task->completed_at?->format('c'),
                'timestamp' => Carbon::now()->format('c')
            ];

            // 使用带重试机制的推送
            $success = $this->pushWithRetry(
                $userId,
                WebSocketSession::EVENT_AI_GENERATION_COMPLETED,
                $eventData,
                "ai_generation_completed:{$taskId}"
            );

            if ($success) {
                Log::info('AI生成完成推送成功', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'task_type' => $task->task_type
                ]);
            }

            return $success;

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('AI生成完成推送异常', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 异常情况下也尝试存储降级消息
            try {
                $eventData = [
                    'task_id' => $taskId,
                    'status' => 'completed',
                    'timestamp' => Carbon::now()->format('c')
                ];
                $this->storeFallbackMessage($userId, WebSocketSession::EVENT_AI_GENERATION_COMPLETED, $eventData);
            } catch (\Exception $fallbackException) {
                Log::error('降级消息存储也失败', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'error' => $fallbackException->getMessage()
                ]);
            }

            return false;
        }
    }

    /**
     * 推送AI生成失败事件
     */
    public function pushAiGenerationFailed(string $taskId, int $userId, string $errorMessage): bool
    {
        try {
            // 通过external_task_id查找任务记录
            $task = AiGenerationTask::where('external_task_id', $taskId)->first();
            if (!$task) {
                Log::warning('未找到任务记录', [
                    'task_id' => $taskId,
                    'user_id' => $userId
                ]);
                return false;
            }

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'status' => $task->status,
                'error_message' => $errorMessage,
                'retry_count' => $task->retry_count,
                'can_retry' => $task->canRetry(),
                'failed_at' => $task->completed_at?->format('c'),
                'timestamp' => Carbon::now()->format('c')
            ];

            $successCount = $this->webSocketService->pushToUser(
                $userId,
                WebSocketSession::EVENT_AI_GENERATION_FAILED,
                $eventData
            );

            Log::info('AI生成失败推送', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error_message' => $errorMessage,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error_message' => $errorMessage,
            ];

            Log::error('AI生成失败推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * 推送积分变动事件
     */
    public function pushPointsChanged(int $userId, string $changeType, float $amount, float $newBalance, string $reason = ''): bool
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return false;
            }

            $eventData = [
                'user_id' => $userId,
                'change_type' => $changeType, // 'increase', 'decrease', 'freeze', 'release'
                'amount' => $amount,
                'new_balance' => $newBalance,
                'frozen_points' => $user->frozen_points,
                'total_points' => $newBalance + $user->frozen_points,
                'reason' => $reason,
                'timestamp' => Carbon::now()->format('c')
            ];

            $successCount = $this->webSocketService->pushToUser(
                $userId,
                WebSocketSession::EVENT_POINTS_CHANGED,
                $eventData
            );

            Log::info('积分变动推送', [
                'user_id' => $userId,
                'change_type' => $changeType,
                'amount' => $amount,
                'new_balance' => $newBalance,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'change_type' => $changeType,
                'amount' => $amount,
                'new_balance' => $newBalance,
                'reason' => $reason,
            ];

            Log::error('积分变动推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * 推送自定义事件
     */
    public function pushCustomEvent(int $userId, string $eventType, array $eventData): bool
    {
        try {
            $data = array_merge($eventData, [
                'timestamp' => Carbon::now()->format('c')
            ]);

            $successCount = $this->webSocketService->pushToUser($userId, $eventType, $data);

            Log::info('自定义事件推送', [
                'user_id' => $userId,
                'event_type' => $eventType,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'event_type' => $eventType,
                'event_data' => $eventData,
            ];

            Log::error('自定义事件推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * 推送系统通知事件
     */
    public function pushSystemNotification(int $userId, string $title, string $message, string $type = 'info'): bool
    {
        try {
            $eventData = [
                'title' => $title,
                'message' => $message,
                'type' => $type, // 'info', 'success', 'warning', 'error'
                'timestamp' => Carbon::now()->format('c')
            ];

            $successCount = $this->webSocketService->pushToUser($userId, 'system_notification', $eventData);

            Log::info('系统通知推送', [
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
            ];

            Log::error('系统通知推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * 批量推送事件到多个用户
     */
    public function pushToMultipleUsers(array $userIds, string $eventType, array $eventData): array
    {
        try {
            $results = [];

            foreach ($userIds as $userId) {
                $results[$userId] = $this->webSocketService->pushToUser($userId, $eventType, $eventData) > 0;
            }

            $successCount = count(array_filter($results));

            Log::info('批量事件推送', [
                'user_count' => count($userIds),
                'event_type' => $eventType,
                'success_count' => $successCount
            ]);

            return $results;

        } catch (\Exception $e) {
            $error_context = [
                'user_ids' => $userIds,
                'event_type' => $eventType,
                'event_data' => $eventData,
            ];

            Log::error('批量事件推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [];
        }
    }

    /**
     * 推送广播事件到所有活跃连接
     */
    public function pushBroadcast(string $eventType, array $eventData): int
    {
        try {
            $activeSessions = WebSocketSession::active()->get();
            $successCount = 0;

            foreach ($activeSessions as $session) {
                if ($this->webSocketService->pushMessage($session->session_id, $eventType, $eventData)) {
                    $successCount++;
                }
            }

            Log::info('广播事件推送', [
                'event_type' => $eventType,
                'total_sessions' => $activeSessions->count(),
                'success_count' => $successCount
            ]);

            return $successCount;

        } catch (\Exception $e) {
            $error_context = [
                'event_type' => $eventType,
                'event_data' => $eventData,
            ];

            Log::error('广播事件推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 0;
        }
    }

    /**
     * 带重试机制的推送方法
     */
    protected function pushWithRetry(int $userId, string $eventType, array $eventData, string $context = ''): bool
    {
        $startTime = microtime(true);
        $attempts = 0;
        $lastException = null;
        $pushId = uniqid('push_');

        // 记录推送开始日志
        Log::info('WebSocket推送开始', [
            'push_id' => $pushId,
            'user_id' => $userId,
            'event_type' => $eventType,
            'context' => $context,
            'data_size' => strlen(json_encode($eventData)),
            'max_attempts' => self::MAX_RETRY_ATTEMPTS,
            'timestamp' => Carbon::now()->format('c')
        ]);

        while ($attempts < self::MAX_RETRY_ATTEMPTS) {
            $attemptStartTime = microtime(true);

            try {
                $successCount = $this->webSocketService->pushToUser($userId, $eventType, $eventData);
                $attemptDuration = round((microtime(true) - $attemptStartTime) * 1000, 2);

                if ($successCount > 0) {
                    // 推送成功，重置断路器
                    $this->resetCircuitBreaker($userId);

                    $totalDuration = round((microtime(true) - $startTime) * 1000, 2);
                    Log::info('WebSocket推送成功', [
                        'push_id' => $pushId,
                        'user_id' => $userId,
                        'event_type' => $eventType,
                        'context' => $context,
                        'attempt' => $attempts + 1,
                        'success_count' => $successCount,
                        'attempt_duration_ms' => $attemptDuration,
                        'total_duration_ms' => $totalDuration,
                        'timestamp' => Carbon::now()->format('c')
                    ]);

                    return true;
                }

                // 推送失败但没有异常，记录并重试
                Log::warning('WebSocket推送失败，准备重试', [
                    'push_id' => $pushId,
                    'user_id' => $userId,
                    'event_type' => $eventType,
                    'context' => $context,
                    'attempt' => $attempts + 1,
                    'success_count' => $successCount,
                    'attempt_duration_ms' => $attemptDuration,
                    'reason' => 'no_active_connections',
                    'timestamp' => Carbon::now()->format('c')
                ]);

            } catch (\Exception $e) {
                $lastException = $e;
                $attemptDuration = round((microtime(true) - $attemptStartTime) * 1000, 2);

                Log::warning('WebSocket推送异常，准备重试', [
                    'push_id' => $pushId,
                    'user_id' => $userId,
                    'event_type' => $eventType,
                    'context' => $context,
                    'attempt' => $attempts + 1,
                    'attempt_duration_ms' => $attemptDuration,
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine(),
                    'timestamp' => Carbon::now()->format('c')
                ]);
            }

            $attempts++;

            // 如果不是最后一次尝试，等待后重试
            if ($attempts < self::MAX_RETRY_ATTEMPTS) {
                $delay = self::RETRY_DELAY_SECONDS * pow(self::EXPONENTIAL_BACKOFF_MULTIPLIER, $attempts - 1);

                Log::info('WebSocket推送重试延迟', [
                    'push_id' => $pushId,
                    'user_id' => $userId,
                    'event_type' => $eventType,
                    'next_attempt' => $attempts + 1,
                    'delay_seconds' => $delay,
                    'timestamp' => Carbon::now()->format('c')
                ]);

                sleep($delay);
            }
        }

        // 所有重试都失败了
        $totalDuration = round((microtime(true) - $startTime) * 1000, 2);
        Log::error('WebSocket推送最终失败', [
            'push_id' => $pushId,
            'user_id' => $userId,
            'event_type' => $eventType,
            'context' => $context,
            'total_attempts' => $attempts,
            'total_duration_ms' => $totalDuration,
            'last_error' => $lastException ? $lastException->getMessage() : 'Unknown error',
            'timestamp' => Carbon::now()->format('c')
        ]);

        $this->handlePushFailure($userId, $eventType, $eventData, $lastException, $context);
        return false;
    }

    /**
     * 处理推送失败的降级策略
     */
    protected function handlePushFailure(int $userId, string $eventType, array $eventData, ?\Exception $exception, string $context): void
    {
        $failureId = uniqid('failure_');

        // 记录失败次数
        $failureCount = $this->incrementFailureCount($userId);

        // 检查是否需要开启断路器
        $circuitBreakerOpened = false;
        if ($this->shouldOpenCircuitBreaker($userId)) {
            $this->openCircuitBreaker($userId);
            $circuitBreakerOpened = true;

            Log::error('WebSocket断路器开启', [
                'failure_id' => $failureId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'context' => $context,
                'failure_count' => $failureCount,
                'threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
                'timeout_seconds' => self::CIRCUIT_BREAKER_TIMEOUT,
                'timestamp' => Carbon::now()->format('c')
            ]);
        }

        // 降级处理：将消息存储到缓存中，供客户端轮询获取
        $fallbackStored = $this->storeFallbackMessage($userId, $eventType, $eventData);

        // 记录详细的失败处理日志
        Log::error('WebSocket推送失败处理完成', [
            'failure_id' => $failureId,
            'user_id' => $userId,
            'event_type' => $eventType,
            'context' => $context,
            'max_attempts' => self::MAX_RETRY_ATTEMPTS,
            'failure_count' => $failureCount,
            'circuit_breaker_opened' => $circuitBreakerOpened,
            'fallback_stored' => $fallbackStored,
            'data_size' => strlen(json_encode($eventData)),
            'error_message' => $exception ? $exception->getMessage() : 'Unknown error',
            'error_type' => $exception ? get_class($exception) : 'Unknown',
            'timestamp' => Carbon::now()->format('c')
        ]);
    }

    /**
     * 存储降级消息到缓存
     */
    protected function storeFallbackMessage(int $userId, string $eventType, array $eventData): bool
    {
        $storageId = uniqid('storage_');

        try {
            $cacheKey = "websocket_fallback:{$userId}";
            $message = [
                'event_type' => $eventType,
                'data' => $eventData,
                'timestamp' => Carbon::now()->format('c'),
                'id' => uniqid('msg_'),
                'storage_id' => $storageId
            ];

            // 获取现有消息列表
            $existingMessages = Cache::get($cacheKey, []);
            $originalCount = count($existingMessages);

            $existingMessages[] = $message;

            // 限制消息数量，只保留最新的50条
            $trimmed = false;
            if (count($existingMessages) > 50) {
                $existingMessages = array_slice($existingMessages, -50);
                $trimmed = true;
            }

            // 存储到缓存
            $cacheStored = Cache::put($cacheKey, $existingMessages, self::FALLBACK_CACHE_TTL);

            Log::info('降级消息存储成功', [
                'storage_id' => $storageId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'message_id' => $message['id'],
                'original_count' => $originalCount,
                'new_count' => count($existingMessages),
                'trimmed' => $trimmed,
                'cache_stored' => $cacheStored,
                'cache_ttl' => self::FALLBACK_CACHE_TTL,
                'data_size' => strlen(json_encode($eventData)),
                'timestamp' => Carbon::now()->format('c')
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('降级消息存储失败', [
                'storage_id' => $storageId,
                'user_id' => $userId,
                'event_type' => $eventType,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'data_size' => strlen(json_encode($eventData)),
                'timestamp' => Carbon::now()->format('c')
            ]);

            return false;
        }
    }

    /**
     * 检查断路器状态
     */
    protected function isCircuitBreakerOpen(int $userId): bool
    {
        $cacheKey = "websocket_circuit_breaker:{$userId}";
        $breakerData = Cache::get($cacheKey);

        if (!$breakerData) {
            return false;
        }

        // 检查断路器是否已过期
        if (Carbon::now()->timestamp > $breakerData['expires_at']) {
            Cache::forget($cacheKey);
            return false;
        }

        return true;
    }

    /**
     * 开启断路器
     */
    protected function openCircuitBreaker(int $userId): void
    {
        $cacheKey = "websocket_circuit_breaker:{$userId}";
        $openedAt = Carbon::now();
        $expiresAt = $openedAt->copy()->addSeconds(self::CIRCUIT_BREAKER_TIMEOUT);

        $breakerData = [
            'opened_at' => $openedAt->timestamp,
            'expires_at' => $expiresAt->timestamp,
            'opened_at_iso' => $openedAt->format('c'),
            'expires_at_iso' => $expiresAt->format('c')
        ];

        $cacheStored = Cache::put($cacheKey, $breakerData, self::CIRCUIT_BREAKER_TIMEOUT + 60);

        Log::warning('WebSocket断路器已开启', [
            'user_id' => $userId,
            'opened_at' => $openedAt->format('c'),
            'expires_at' => $expiresAt->format('c'),
            'timeout_seconds' => self::CIRCUIT_BREAKER_TIMEOUT,
            'cache_stored' => $cacheStored,
            'timestamp' => Carbon::now()->format('c')
        ]);
    }

    /**
     * 重置断路器
     */
    protected function resetCircuitBreaker(int $userId): void
    {
        $circuitBreakerKey = "websocket_circuit_breaker:{$userId}";
        $failureCountKey = "websocket_failure_count:{$userId}";

        $hadCircuitBreaker = Cache::has($circuitBreakerKey);
        $hadFailureCount = Cache::has($failureCountKey);

        Cache::forget($circuitBreakerKey);
        Cache::forget($failureCountKey);

        if ($hadCircuitBreaker || $hadFailureCount) {
            Log::info('WebSocket断路器已重置', [
                'user_id' => $userId,
                'had_circuit_breaker' => $hadCircuitBreaker,
                'had_failure_count' => $hadFailureCount,
                'timestamp' => Carbon::now()->format('c')
            ]);
        }
    }

    /**
     * 增加失败计数
     */
    protected function incrementFailureCount(int $userId): int
    {
        $cacheKey = "websocket_failure_count:{$userId}";
        $oldCount = Cache::get($cacheKey, 0);
        $newCount = $oldCount + 1;

        $cacheStored = Cache::put($cacheKey, $newCount, 300); // 5分钟过期

        Log::warning('WebSocket失败计数增加', [
            'user_id' => $userId,
            'old_count' => $oldCount,
            'new_count' => $newCount,
            'threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
            'cache_stored' => $cacheStored,
            'timestamp' => Carbon::now()->format('c')
        ]);

        return $newCount;
    }

    /**
     * 检查是否应该开启断路器
     */
    protected function shouldOpenCircuitBreaker(int $userId): bool
    {
        $cacheKey = "websocket_failure_count:{$userId}";
        $count = Cache::get($cacheKey, 0);
        return $count >= self::CIRCUIT_BREAKER_THRESHOLD;
    }

    /**
     * 获取用户的降级消息（供客户端轮询使用）
     */
    public function getFallbackMessages(int $userId, ?string $lastMessageId = null): array
    {
        $requestId = uniqid('get_fallback_');
        $startTime = microtime(true);

        try {
            $cacheKey = "websocket_fallback:{$userId}";
            $allMessages = Cache::get($cacheKey, []);
            $originalCount = count($allMessages);

            // 如果提供了lastMessageId，只返回该ID之后的消息
            $filteredMessages = $allMessages;
            $foundIndex = -1;

            if ($lastMessageId) {
                foreach ($allMessages as $index => $message) {
                    if ($message['id'] === $lastMessageId) {
                        $foundIndex = $index;
                        break;
                    }
                }

                if ($foundIndex >= 0) {
                    $filteredMessages = array_slice($allMessages, $foundIndex + 1);
                }
            }

            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('获取降级消息成功', [
                'request_id' => $requestId,
                'user_id' => $userId,
                'last_message_id' => $lastMessageId,
                'original_count' => $originalCount,
                'filtered_count' => count($filteredMessages),
                'found_index' => $foundIndex,
                'duration_ms' => $duration,
                'timestamp' => Carbon::now()->format('c')
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取降级消息成功',
                'data' => [
                    'messages' => $filteredMessages,
                    'total_count' => count($filteredMessages),
                    'has_more' => $foundIndex >= 0 && count($filteredMessages) > 0,
                    'timestamp' => Carbon::now()->format('c')
                ]
            ];

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::error('获取降级消息失败', [
                'request_id' => $requestId,
                'user_id' => $userId,
                'last_message_id' => $lastMessageId,
                'duration_ms' => $duration,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => Carbon::now()->format('c')
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取降级消息失败',
                'data' => null
            ];
        }
    }

    /**
     * 清除用户的降级消息
     */
    public function clearFallbackMessages(int $userId): bool
    {
        try {
            $cacheKey = "websocket_fallback:{$userId}";
            Cache::forget($cacheKey);

            Log::info('清除降级消息成功', [
                'user_id' => $userId
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('清除降级消息失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 获取WebSocket连接状态和统计信息
     */
    public function getConnectionStatus(int $userId): array
    {
        try {
            $circuitBreakerOpen = $this->isCircuitBreakerOpen($userId);
            $failureCount = Cache::get("websocket_failure_count:{$userId}", 0);
            $fallbackMessageCount = count(Cache::get("websocket_fallback:{$userId}", []));

            return [
                'user_id' => $userId,
                'circuit_breaker_open' => $circuitBreakerOpen,
                'failure_count' => $failureCount,
                'fallback_message_count' => $fallbackMessageCount,
                'max_retry_attempts' => self::MAX_RETRY_ATTEMPTS,
                'circuit_breaker_threshold' => self::CIRCUIT_BREAKER_THRESHOLD,
                'timestamp' => Carbon::now()->format('c')
            ];

        } catch (\Exception $e) {
            Log::error('获取连接状态失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'user_id' => $userId,
                'error' => '获取状态失败',
                'timestamp' => Carbon::now()->format('c')
            ];
        }
    }
}
