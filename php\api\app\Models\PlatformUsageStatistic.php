<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * 平台使用统计模型
 * 
 * @property int $id
 * @property int $user_id
 * @property string $task_type
 * @property string $platform
 * @property int $usage_count
 * @property int $success_count
 * @property int $failure_count
 * @property float $total_cost
 * @property float $average_response_time
 * @property float $user_satisfaction_score
 * @property int $user_rating_count
 * @property array $performance_metrics
 * @property array $error_details
 * @property \Carbon\Carbon $last_used_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class PlatformUsageStatistic extends Model
{
    /**
     * 表名
     */
    protected $table = 'platform_usage_statistics';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'task_type',
        'platform',
        'usage_count',
        'success_count',
        'failure_count',
        'total_cost',
        'average_response_time',
        'user_satisfaction_score',
        'user_rating_count',
        'performance_metrics',
        'error_details',
        'last_used_at'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'usage_count' => 'integer',
        'success_count' => 'integer',
        'failure_count' => 'integer',
        'total_cost' => 'decimal:4',
        'average_response_time' => 'decimal:2',
        'user_satisfaction_score' => 'decimal:2',
        'user_rating_count' => 'integer',
        'performance_metrics' => 'array',
        'error_details' => 'array',
        'last_used_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 记录平台使用
     */
    public function recordUsage(bool $success, float $cost, float $responseTime, array $metrics = []): void
    {
        $this->increment('usage_count');
        
        if ($success) {
            $this->increment('success_count');
        } else {
            $this->increment('failure_count');
        }

        // 更新总成本
        $this->total_cost += $cost;

        // 更新平均响应时间
        $this->updateAverageResponseTime($responseTime);

        // 更新性能指标
        $this->updatePerformanceMetrics($metrics);

        $this->last_used_at = now();
        $this->save();
    }

    /**
     * 更新平均响应时间
     */
    private function updateAverageResponseTime(float $responseTime): void
    {
        if ($this->usage_count <= 1) {
            $this->average_response_time = $responseTime;
        } else {
            // 计算加权平均值
            $totalTime = $this->average_response_time * ($this->usage_count - 1) + $responseTime;
            $this->average_response_time = $totalTime / $this->usage_count;
        }
    }

    /**
     * 更新性能指标
     */
    private function updatePerformanceMetrics(array $metrics): void
    {
        $currentMetrics = $this->performance_metrics ?? [];
        
        foreach ($metrics as $key => $value) {
            if (!isset($currentMetrics[$key])) {
                $currentMetrics[$key] = ['total' => 0, 'count' => 0, 'average' => 0];
            }
            
            $currentMetrics[$key]['total'] += $value;
            $currentMetrics[$key]['count']++;
            $currentMetrics[$key]['average'] = $currentMetrics[$key]['total'] / $currentMetrics[$key]['count'];
        }
        
        $this->performance_metrics = $currentMetrics;
    }

    /**
     * 记录用户评分
     */
    public function recordUserRating(float $rating): void
    {
        $currentScore = $this->user_satisfaction_score ?? 0;
        $currentCount = $this->user_rating_count ?? 0;
        
        $newTotal = ($currentScore * $currentCount) + $rating;
        $newCount = $currentCount + 1;
        
        $this->user_satisfaction_score = $newTotal / $newCount;
        $this->user_rating_count = $newCount;
        $this->save();
    }

    /**
     * 记录错误详情
     */
    public function recordError(string $errorType, string $errorMessage, array $context = []): void
    {
        $errors = $this->error_details ?? [];
        
        if (!isset($errors[$errorType])) {
            $errors[$errorType] = ['count' => 0, 'messages' => [], 'last_occurred' => null];
        }
        
        $errors[$errorType]['count']++;
        $errors[$errorType]['messages'][] = [
            'message' => $errorMessage,
            'context' => $context,
            'occurred_at' => now()->format('c')
        ];
        $errors[$errorType]['last_occurred'] = now()->format('c');
        
        // 只保留最近10条错误消息
        if (count($errors[$errorType]['messages']) > 10) {
            $errors[$errorType]['messages'] = array_slice($errors[$errorType]['messages'], -10);
        }
        
        $this->error_details = $errors;
        $this->save();
    }

    /**
     * 获取成功率
     */
    public function getSuccessRate(): float
    {
        if ($this->usage_count === 0) {
            return 0.0;
        }
        
        return round(($this->success_count / $this->usage_count) * 100, 2);
    }

    /**
     * 获取失败率
     */
    public function getFailureRate(): float
    {
        if ($this->usage_count === 0) {
            return 0.0;
        }
        
        return round(($this->failure_count / $this->usage_count) * 100, 2);
    }

    /**
     * 获取平均成本
     */
    public function getAverageCost(): float
    {
        if ($this->usage_count === 0) {
            return 0.0;
        }
        
        return round($this->total_cost / $this->usage_count, 4);
    }

    /**
     * 获取性能评分
     */
    public function getPerformanceScore(): float
    {
        $successRate = $this->getSuccessRate() / 100; // 转换为0-1
        $responseScore = $this->average_response_time > 0 ? min(1.0, 30 / $this->average_response_time) : 0; // 30秒内响应得满分
        $satisfactionScore = $this->user_satisfaction_score / 5; // 转换为0-1（假设评分是1-5）
        
        // 加权计算综合评分
        return round(($successRate * 0.4 + $responseScore * 0.3 + $satisfactionScore * 0.3) * 100, 2);
    }

    /**
     * 任务类型常量
     */
    const TASK_TYPE_IMAGE_GENERATION = 'image_generation';
    const TASK_TYPE_VIDEO_GENERATION = 'video_generation';
    const TASK_TYPE_VOICE_SYNTHESIS = 'voice_synthesis';
    const TASK_TYPE_SOUND_GENERATION = 'sound_generation';
    const TASK_TYPE_MUSIC_GENERATION = 'music_generation';
    const TASK_TYPE_TEXT_GENERATION = 'text_generation';

    /**
     * 获取所有任务类型
     */
    public static function getTaskTypes(): array
    {
        return [
            self::TASK_TYPE_IMAGE_GENERATION,
            self::TASK_TYPE_VIDEO_GENERATION,
            self::TASK_TYPE_VOICE_SYNTHESIS,
            self::TASK_TYPE_SOUND_GENERATION,
            self::TASK_TYPE_MUSIC_GENERATION,
            self::TASK_TYPE_TEXT_GENERATION
        ];
    }

    /**
     * 创建或更新统计记录
     */
    public static function createOrUpdate(int $userId, string $taskType, string $platform): self
    {
        return self::firstOrCreate(
            [
                'user_id' => $userId,
                'task_type' => $taskType,
                'platform' => $platform
            ],
            [
                'usage_count' => 0,
                'success_count' => 0,
                'failure_count' => 0,
                'total_cost' => 0,
                'average_response_time' => 0,
                'user_satisfaction_score' => 0,
                'user_rating_count' => 0,
                'performance_metrics' => [],
                'error_details' => []
            ]
        );
    }

    /**
     * 范围查询：按任务类型
     */
    public function scopeByTaskType($query, string $taskType)
    {
        return $query->where('task_type', $taskType);
    }

    /**
     * 范围查询：按平台
     */
    public function scopeByPlatform($query, string $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * 范围查询：按用户ID
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：按时间范围
     */
    public function scopeInDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('last_used_at', [$startDate, $endDate]);
    }

    /**
     * 范围查询：高成功率
     */
    public function scopeHighSuccessRate($query, float $minRate = 80.0)
    {
        return $query->whereRaw('(success_count / usage_count * 100) >= ?', [$minRate]);
    }

    /**
     * 范围查询：高满意度
     */
    public function scopeHighSatisfaction($query, float $minScore = 4.0)
    {
        return $query->where('user_satisfaction_score', '>=', $minScore);
    }
}
