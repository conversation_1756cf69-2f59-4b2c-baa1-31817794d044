<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Models\CharacterLibrary;
use App\Services\PyApi\CharacterService;
use App\Services\PyApi\WebSocketService;
use App\Services\PyApi\WebSocketEventService;
use App\Jobs\ProcessCharacterCreation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Helpers\LogCheckHelper;

/**
 * 角色库管理与绑定系统
 */
class CharacterController extends Controller
{
    protected $characterService;

    public function __construct(CharacterService $characterService)
    {
        $this->characterService = $characterService;
    }

    /**
     * @ApiTitle(角色分类列表)
     * @ApiSummary(获取角色分类列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/categories)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="parent_id", type="int", required=false, description="父分类ID")
     * @ApiParams(name="include_children", type="boolean", required=false, description="是否包含子分类")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="array", required=true, description="分类列表")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "动漫角色",
     *       "slug": "anime",
     *       "description": "来自动漫的角色",
     *       "icon": "https://example.com/icon.png",
     *       "character_count": 50,
     *       "children": []
     *     }
     *   ]
     * })
     */
    public function getCategories(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 正确处理parent_id参数类型转换
            $parentId = $request->get('parent_id');
            if ($parentId !== null && $parentId !== '') {
                $parentId = (int) $parentId;
            } else {
                $parentId = null;
            }

            $includeChildren = $request->boolean('include_children', false);

            $result = $this->characterService->getCategories($parentId, $includeChildren);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取角色分类失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色分类失败', []);
        }
    }

    /**
     * @ApiTitle(角色列表)
     * @ApiSummary(获取角色库列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/list)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="category_id", type="int", required=false, description="分类ID")
     * @ApiParams(name="gender", type="string", required=false, description="性别筛选")
     * @ApiParams(name="is_premium", type="boolean", required=false, description="是否高级角色")
     * @ApiParams(name="is_featured", type="boolean", required=false, description="是否推荐角色")
     * @ApiParams(name="keyword", type="string", required=false, description="搜索关键词")
     * @ApiParams(name="tags", type="string", required=false, description="标签筛选，逗号分隔")
     * @ApiParams(name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "characters": [
     *       {
     *         "id": 1,
     *         "name": "小樱",
     *         "description": "可爱的魔法少女",
     *         "category": "动漫角色",
     *         "gender": "female",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "is_premium": false,
     *         "rating": 4.5,
     *         "binding_count": 100
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 100,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getLibrary(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $filters = [
                'tab' => $request->get('tab', 'public'),
                'gender' => $request->get('gender'),
                'age_range' => $request->get('age_range'),
                'search' => $request->get('search'),
                'is_premium' => $request->get('is_premium'),
                'is_featured' => $request->get('is_featured'),
                'tags' => $request->get('tags') ? explode(',', $request->get('tags')) : null
            ];

            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);

            $result = $this->characterService->getCharacters($filters, $page, $perPage);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取角色库失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色库失败', []);
        }
    }

    /**
     * @ApiTitle(角色详情)
     * @ApiSummary(获取角色详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="角色ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="角色详情")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "name": "小樱",
     *     "description": "可爱的魔法少女",
     *     "category": {
     *       "id": 1,
     *       "name": "动漫角色"
     *     },
     *     "gender": "female",
     *     "age_range": "16-18",
     *     "personality": "活泼开朗，善良勇敢",
     *     "background": "来自卡牌捕获者樱的主角",
     *     "appearance": "粉色短发，绿色眼睛",
     *     "avatar": "https://example.com/avatar.jpg",
     *     "images": ["https://example.com/img1.jpg"],
     *     "voice_config": {},
     *     "style_preferences": {},
     *     "tags": ["魔法", "少女", "可爱"],
     *     "rating": 4.5,
     *     "binding_count": 100,
     *     "is_bound": false
     *   }
     * })
     */
    public function getCharacterDetail(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 🚨 CogniDev修复：类型转换问题，确保$id为int类型
            $characterId = (int) $id;
            if ($characterId <= 0) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '无效的角色ID');
            }

            $user = $authResult['user'];
            $result = $this->characterService->getCharacterDetail($characterId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取角色详情失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色详情失败', []);
        }
    }

    /**
     * @ApiTitle(角色生成)
     * @ApiSummary(使用AI生成角色描述)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/characters/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompt", type="string", required=true, description="角色生成提示词")
     * @ApiParams(name="gender", type="string", required=false, description="性别：male/female/other")
     * @ApiParams(name="age_range", type="string", required=false, description="年龄范围")
     * @ApiParams(name="personality", type="string", required=false, description="性格特征")
     * @ApiParams(name="platform", type="string", required=false, description="指定AI平台：deepseek/minimax")
     * @ApiParams(name="platform_config", type="object", required=false, description="平台特定配置")
     * @ApiParams(name="style", type="string", required=false, description="角色风格：写实风3.0/动漫可爱3.0/武侠古风3.0等24种风格")
     * @ApiParams(name="project_id", type="integer", required=false, description="项目ID，用于关联项目")
     * @ApiParams(name="mode", type="string", required=false, description="调用模式：full/filtered/quick/advanced")
     * @ApiParams(name="quality_level", type="string", required=false, description="质量偏好：high/medium/low")
     * @ApiParams(name="publish_to_library", type="boolean", required=false, description="是否发布到角色库")
     * @ApiParams(name="auto_bind", type="boolean", required=false, description="是否自动绑定")
     * @ApiParams(name="storyboard_position_id", type="string", required=false, description="分镜位置ID，用于自动绑定")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.character_info", type="object", required=false, description="生成的角色信息")
     * @ApiReturnParams (name="data.cost", type="decimal", required=false, description="消耗的积分")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "角色生成成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "completed",
     *     "character_info": {
     *       "name": "艾莉丝",
     *       "description": "一个勇敢的冒险家",
     *       "personality": "勇敢、善良、聪明",
     *       "background": "来自魔法世界的少女",
     *       "appearance": "金色长发，蓝色眼睛",
     *       "skills": ["魔法", "剑术", "治疗"],
     *       "relationships": []
     *     },
     *     "cost": "0.0100"
     *   }
     * })
     */
    public function generate(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先执行认证检查
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证 - 在认证通过后执行
            $rules = [
                'prompt' => 'required|string|min:5|max:1000',
                'gender' => 'sometimes|string|in:male,female,other',
                'age_range' => 'sometimes|string|max:20',
                'personality' => 'sometimes|string|max:500',
                'platform' => 'sometimes|string|in:deepseek,minimax',
                'platform_config' => 'sometimes|array',
                'style' => 'sometimes|string|max:100',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'mode' => 'sometimes|string|in:full,filtered,quick,advanced',
                'quality_level' => 'sometimes|string|in:high,medium,low',
                'publish_to_library' => 'sometimes|boolean',
                'auto_bind' => 'sometimes|boolean',
                'storyboard_position_id' => 'sometimes|string|max:100'
            ];

            $messages = [
                'prompt.required' => '角色生成提示词不能为空',
                'prompt.min' => '角色生成提示词至少5个字符',
                'prompt.max' => '角色生成提示词不能超过1000个字符',
                'gender.in' => '性别必须是：male、female、other之一',
                'personality.max' => '性格特征不能超过500个字符',
                'platform.in' => 'AI平台必须是：deepseek、minimax之一',
                'platform_config.array' => '平台配置必须是数组格式',
                'style.max' => '角色风格名称不能超过100个字符',
                'project_id.exists' => '项目不存在',
                'mode.in' => '调用模式必须是：full、filtered、quick、advanced之一',
                'quality_level.in' => '质量偏好必须是：high、medium、low之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];

            $generationParams = [
                'gender' => $request->get('gender'),
                'age_range' => $request->get('age_range'),
                'personality' => $request->get('personality'),
                'platform' => $request->get('platform', 'deepseek'),
                'platform_config' => $request->get('platform_config', []),
                'style' => $request->get('style'),
                'mode' => $request->get('mode', 'full'),
                'quality_level' => $request->get('quality_level', 'medium'),
                'publish_to_library' => $request->get('publish_to_library', false),
                'auto_bind' => $request->get('auto_bind', false),
                'storyboard_position_id' => $request->get('storyboard_position_id')
            ];

            $result = $this->characterService->generateCharacter(
                $user->id,
                $request->prompt,
                $request->get('project_id'), // 恢复project_id支持
                $generationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('生成角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '生成角色失败', []);
        }
    }

    /**
     * @ApiTitle(基于文件创建角色)
     * @ApiSummary(基于上传的文件创建角色)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/characters/create-from-file)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="file_id", type="string", required=true, description="文件ID，通过FileController上传获得")
     * @ApiParams(name="style", type="string", required=false, description="角色风格：写实风3.0/动漫可爱3.0等")
     * @ApiParams(name="project_id", type="integer", required=false, description="项目ID，用于关联项目")
     * @ApiParams(name="publish_to_library", type="boolean", required=false, description="是否发布到角色库")
     * @ApiParams(name="auto_bind", type="boolean", required=false, description="是否自动绑定")
     * @ApiParams(name="storyboard_position_id", type="string", required=false, description="分镜位置ID，用于自动绑定")
     * @ApiReturn(type="object", sample="{
     *   'code': 200,
     *   'message': '角色创建成功',
     *   'data': {
     *     'character_id': 'char_123456',
     *     'name': '角色名称',
     *     'image_url': 'https://example.com/character.jpg',
     *     'style': '写实风3.0',
     *     'created_at': '2024-01-01 12:00:00'
     *   }
     * }")
     */
    public function createFromFile(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证
            $rules = [
                'file_id' => 'required|integer|exists:user_files,id',
                'style' => 'sometimes|string|max:100',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'publish_to_library' => 'sometimes|boolean',
                'auto_bind' => 'sometimes|boolean',
                'storyboard_position_id' => 'sometimes|string|max:100'
            ];

            $messages = [
                'file_id.required' => '文件ID不能为空',
                'file_id.exists' => '文件不存在',
                'style.max' => '角色风格名称不能超过100个字符',
                'project_id.exists' => '项目不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];

            // 验证文件是否为有效的图片文件
            $file = \App\Models\UserFile::find($request->file_id);
            if (!$file) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '文件不存在');
            }

            // 检查文件类型是否为图片
            if (!$file->isImage()) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '文件必须是图片格式');
            }

            $creationParams = [
                'file_id' => $request->file_id,
                'style' => $request->get('style'),
                'publish_to_library' => $request->get('publish_to_library', false),
                'auto_bind' => $request->get('auto_bind', false),
                'storyboard_position_id' => $request->get('storyboard_position_id')
            ];

            $result = $this->characterService->createCharacterFromFile(
                $user->id,
                $request->get('project_id'),
                $creationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('基于文件创建角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '基于文件创建角色失败', []);
        }
    }

    /**
     * @ApiTitle(推荐角色)
     * @ApiSummary(获取个性化推荐角色)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/recommendations)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="limit", type="int", required=false, description="推荐数量，默认10")
     * @ApiParams(name="type", type="string", required=false, description="推荐类型：popular,similar,new")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="推荐数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "recommendations": [
     *       {
     *         "id": 1,
     *         "name": "小樱",
     *         "description": "可爱的魔法少女",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "rating": 4.5,
     *         "reason": "基于您的偏好推荐"
     *       }
     *     ],
     *     "recommendation_type": "popular"
     *   }
     * })
     */
    public function getRecommendations(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $limit = min($request->get('limit', 10), 50);
            $type = $request->get('type', 'popular');

            $result = $this->characterService->getRecommendations($user->id, $limit, $type);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message'], $result['code']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data'] ?? null);
            }
        } catch (\Exception $e) {
            Log::error('获取角色推荐失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取角色推荐失败', []);
        }
    }

    /**
     * @ApiTitle(角色绑定)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/characters/bind)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="character_id", type="integer", required=true, description="角色ID")
     * @ApiParams(name="reason", type="string", required=false, description="绑定原因")
     * @ApiParams(name="storyboard_position_id", type="string", required=false, description="分镜位置ID，用于分镜绑定")
     * @ApiParams(name="binding_context", type="string", required=false, description="绑定上下文：storyboard/project/library")
     * @ApiParams(name="auto_bind", type="boolean", required=false, description="是否自动绑定标识")
     * @ApiParams(name="compatibility_check", type="boolean", required=false, description="是否进行兼容性检查")
     * @ApiReturn({"code": 200, "message": "角色绑定成功", "data": {"binding_id": 123, "storyboard_position_id": "pos_123"}})
     */
    public function bindCharacter(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先执行认证检查
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证 - 在认证通过后执行，先验证基本参数，再检查资源存在性
            $rules = [
                'character_id' => 'required|integer',
                'reason' => 'sometimes|string|max:200',
                'storyboard_position_id' => 'sometimes|string|max:100',
                'binding_context' => 'sometimes|string|in:storyboard,project,library',
                'auto_bind' => 'sometimes|boolean',
                'compatibility_check' => 'sometimes|boolean'
            ];

            $messages = [
                'character_id.required' => '角色ID不能为空',
                'character_id.integer' => '角色ID必须是整数',
                'reason.max' => '绑定原因不能超过200个字符',
                'storyboard_position_id.max' => '分镜位置ID不能超过100个字符',
                'binding_context.in' => '绑定上下文必须是：storyboard、project、library之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];
            $characterId = $request->input('character_id');
            $reason = $request->input('reason');

            // 手动检查角色存在性，确保返回正确的404状态码
            $character = CharacterLibrary::find($characterId);
            if (!$character) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '角色不存在');
            }

            // 准备绑定参数
            $bindingParams = [
                'reason' => $reason,
                'storyboard_position_id' => $request->get('storyboard_position_id'),
                'binding_context' => $request->get('binding_context', 'library'),
                'auto_bind' => $request->get('auto_bind', false),
                'compatibility_check' => $request->get('compatibility_check', true)
            ];

            // 调用角色绑定服务
            $result = $this->characterService->bindCharacter($user->id, $characterId, $bindingParams);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('绑定角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '绑定角色失败', []);
        }
    }

    /**
     * @ApiTitle(获取我的角色绑定)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/characters/my-bindings)
     * @ApiReturn({"code": 200, "message": "success", "data": {"bindings": []}})
     */
    public function getMyBindings(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $filters = [
                'is_favorite' => $request->get('is_favorite'),
                'category_id' => $request->get('category_id'),
                'sort' => $request->get('sort', 'usage')
            ];

            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);
            $result = $this->characterService->getUserBindings($user->id, $filters, $page, $perPage);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取我的角色绑定失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取我的角色绑定失败', []);
        }
    }

    /**
     * @ApiTitle(更新角色绑定)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/characters/bindings/{id})
     * @ApiParams({"id": "绑定ID"})
     * @ApiReturn({"code": 200, "message": "绑定更新成功", "data": {}})
     */
    public function updateBinding(Request $request, $id)
    {
        try {
            $rules = [
                'binding_name' => 'sometimes|string|max:100',
                'custom_description' => 'sometimes|string|max:1000',
                'custom_config' => 'sometimes|array',
                'is_favorite' => 'sometimes|boolean'
            ];

            // 检查是否至少提供了一个更新参数
            $hasUpdateParams = $request->hasAny(['binding_name', 'custom_description', 'custom_config', 'is_favorite']);
            if (!$hasUpdateParams) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '至少需要提供一个更新参数');
            }

            $messages = [
                'binding_name.max' => '绑定名称不能超过100个字符',
                'custom_description.max' => '自定义描述不能超过1000个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 手动检查绑定存在性，确保返回正确的404状态码
            $binding = \App\Models\UserCharacterBinding::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$binding) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '绑定不存在');
            }

            $updateData = $request->only([
                'binding_name', 'custom_description', 'custom_config', 'is_favorite'
            ]);

            $result = $this->characterService->updateBinding($id, $user->id, $updateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('更新角色绑定失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新角色绑定失败', []);
        }
    }

    /**
     * @ApiTitle(解绑角色)
     * @ApiMethod(DELETE)
     * @ApiRoute(/py-api/characters/unbind)
     * @ApiParams({"character_id": "角色ID"})
     * @ApiReturn({"code": 200, "message": "角色解绑成功", "data": {"character_id": 1}})
     */
    public function unbindCharacter(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先执行认证检查
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证 - 在认证通过后执行，先验证基本参数，再检查资源存在性
            $rules = [
                'character_id' => 'required|integer'
            ];

            $messages = [
                'character_id.required' => '角色ID不能为空',
                'character_id.integer' => '角色ID必须是整数'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];
            $characterId = $request->input('character_id');

            // 手动检查角色存在性，确保返回正确的404状态码
            $character = CharacterLibrary::find($characterId);
            if (!$character) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '角色不存在');
            }

            // 先查找绑定ID
            $binding = \App\Models\UserCharacterBinding::where('user_id', $user->id)
                ->where('character_id', $characterId)
                ->first();

            if (!$binding) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '绑定不存在');
            }

            $result = $this->characterService->unbindCharacter($binding->id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('解绑角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '解绑角色失败', []);
        }
    }

    /**
     * 生成角色图像
     * @ApiTitle(生成角色图像)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/characters/generate-image)
     * @ApiParams(name="prompt", type="string", required=true, description="角色描述提示词")
     * @ApiParams(name="project_id", type="int", required=false, description="项目ID")
     * @ApiParams(name="generation_params", type="object", required=false, description="生成参数")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=true, description="角色图像数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "角色图像生成成功",
     *   "data": {
     *     "image_url": "https://example.com/character.jpg",
     *     "thumbnail_url": "https://example.com/character_thumb.jpg",
     *     "resource_id": 123,
     *     "platform": "liblib",
     *     "cost": 10.5
     *   }
     * })
     */
    public function generateImage(Request $request)
    {
        try {
            // 参数验证
            $rules = [
                'prompt' => 'required|string|max:1000',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'generation_params' => 'sometimes|array',
                'generation_params.style' => 'sometimes|string|max:100',
                'generation_params.gender' => 'sometimes|string|in:男性,女性,其他',
                'generation_params.age_range' => 'sometimes|string|max:50',
                'generation_params.character_type' => 'sometimes|string|max:100',
                'generation_params.platform' => 'sometimes|string|max:50',
                'generation_params.quality' => 'sometimes|string|in:standard,hd,ultra',
                'generation_params.aspect_ratio' => 'sometimes|string|in:1:1,4:3,3:4,16:9,9:16'
            ];

            $messages = [
                'prompt.required' => '角色描述提示词不能为空',
                'prompt.max' => '角色描述提示词不能超过1000个字符',
                'project_id.exists' => '项目不存在',
                'generation_params.gender.in' => '性别参数无效',
                'generation_params.quality.in' => '质量参数无效',
                'generation_params.aspect_ratio.in' => '宽高比参数无效'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $generationParams = $request->get('generation_params', []);

            $result = $this->characterService->generateCharacterImage(
                $user->id,
                $request->prompt,
                $request->get('project_id'),
                $generationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('生成角色图像失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '生成角色图像失败', []);
        }
    }

    /**
     * 创建角色（WebSocket版本）
     * @ApiTitle(创建角色-WebSocket版本)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/characters/create-with-websocket)
     * @ApiParams(name="mode", type="string", required=true, description="创建模式：intelligent或custom")
     * @ApiParams(name="character_data", type="object", required=true, description="角色数据")
     * @ApiParams(name="project_id", type="int", required=false, description="项目ID")
     * @ApiParams(name="websocket_session_id", type="string", required=false, description="WebSocket会话ID")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=true, description="任务信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "角色创建任务创建成功",
     *   "data": {
     *     "task_id": "character_123456",
     *     "status": "processing",
     *     "mode": "intelligent",
     *     "estimated_cost": 15.0
     *   }
     * })
     */
    public function createWithWebSocket(Request $request)
    {
        try {
            // 参数验证
            $rules = [
                'mode' => 'required|string|in:intelligent,custom',
                'character_data' => 'required|array',
                'character_data.name' => 'sometimes|string|max:100',
                'character_data.description' => 'sometimes|string|max:1000',
                'character_data.prompt' => 'required_if:mode,intelligent|string|max:1000',
                'character_data.file_id' => 'required_if:mode,custom|integer|exists:user_files,id',
                'character_data.gender' => 'sometimes|string|in:男性,女性,其他',
                'character_data.age_range' => 'sometimes|string|max:50',
                'character_data.character_type' => 'sometimes|string|max:100',
                'character_data.style' => 'sometimes|string|max:100',
                'character_data.generation_params' => 'sometimes|array',
                'character_data.publish_to_library' => 'sometimes|boolean',
                'character_data.auto_bind' => 'sometimes|boolean',
                'character_data.storyboard_position_id' => 'sometimes|string|max:100',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'websocket_session_id' => 'sometimes|string'
            ];

            $messages = [
                'mode.required' => '创建模式不能为空',
                'mode.in' => '创建模式必须是intelligent或custom',
                'character_data.required' => '角色数据不能为空',
                'character_data.prompt.required_if' => '智能模式需要提供角色描述提示词',
                'character_data.file_id.required_if' => '自定义模式需要提供文件ID',
                'character_data.file_id.exists' => '文件不存在',
                'project_id.exists' => '项目不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $mode = $request->get('mode');
            $characterData = $request->get('character_data');
            $projectId = $request->get('project_id');
            $webSocketSessionId = $request->get('websocket_session_id');

            // 如果有WebSocket会话ID，验证会话
            if ($webSocketSessionId) {
                $webSocketService = app(WebSocketService::class);
                $sessionValid = $webSocketService->validateSession($webSocketSessionId, $user->id);
                if (!$sessionValid) {
                    return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, 'WebSocket会话无效', []);
                }
            }

            // 生成任务ID
            $taskId = 'character_' . time() . '_' . Str::random(8);

            // 创建异步任务
            ProcessCharacterCreation::dispatch(
                $taskId,
                $user->id,
                $characterData,
                $projectId,
                $mode
            );

            // 立即返回任务信息
            $responseData = [
                'task_id' => $taskId,
                'status' => 'processing',
                'mode' => $mode,
                'estimated_cost' => $mode === 'intelligent' ? 15.0 : 5.0,
                'character_data' => $characterData,
                'project_id' => $projectId,
                'websocket_session_id' => $webSocketSessionId,
                'timestamp' => now()->format('c'),
                'request_id' => 'req_' . Str::random(16)
            ];

            Log::info('角色创建任务创建成功', [
                'task_id' => $taskId,
                'user_id' => $user->id,
                'mode' => $mode,
                'project_id' => $projectId
            ]);

            return $this->successResponse($responseData, '角色创建任务创建成功');

        } catch (\Exception $e) {
            Log::error('创建角色任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建角色任务失败', []);
        }
    }
}
