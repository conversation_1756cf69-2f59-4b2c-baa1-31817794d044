<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 项目场景模型
 * 存储从AI返回的分镜剧本中提取的场景信息
 * 
 * @property int $id
 * @property int $project_id
 * @property string $scene_name
 * @property string $space
 * @property string $time
 * @property string $weather
 * @property string $scene_prompt
 * @property int $scene_order
 * @property array $metadata
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class ProjectScenario extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'project_scenarios';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'project_id',
        'scene_name',
        'space',
        'time',
        'weather',
        'scene_prompt',
        'scene_order',
        'metadata'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 空间类型常量
     */
    const SPACE_INDOOR = '室内';
    const SPACE_OUTDOOR = '室外';

    /**
     * 关联项目
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * 关联分镜
     */
    public function storyboards(): HasMany
    {
        return $this->hasMany(ProjectStoryboard::class, 'scenarios_id');
    }

    /**
     * 查询作用域：按项目筛选
     */
    public function scopeByProject($query, int $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * 查询作用域：按场景顺序排序
     */
    public function scopeOrderByScene($query)
    {
        return $query->orderBy('scene_order');
    }

    /**
     * 查询作用域：按空间类型筛选
     */
    public function scopeBySpace($query, string $space)
    {
        return $query->where('space', $space);
    }

    /**
     * 获取场景元数据
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * 设置场景元数据
     */
    public function setMetadataValue(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
        $this->save();
    }

    /**
     * 获取场景的分镜数量
     */
    public function getStoryboardCount(): int
    {
        return $this->storyboards()->count();
    }

    /**
     * 检查是否为室内场景
     */
    public function isIndoor(): bool
    {
        return $this->space === self::SPACE_INDOOR;
    }

    /**
     * 检查是否为室外场景
     */
    public function isOutdoor(): bool
    {
        return $this->space === self::SPACE_OUTDOOR;
    }

    /**
     * 转换为API数组格式
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'scene_name' => $this->scene_name,
            'space' => $this->space,
            'time' => $this->time,
            'weather' => $this->weather,
            'scene_prompt' => $this->scene_prompt,
            'scene_order' => $this->scene_order,
            'storyboard_count' => $this->getStoryboardCount(),
            'metadata' => $this->metadata,
            'created_at' => $this->created_at?->format('c'),
            'updated_at' => $this->updated_at?->format('c')
        ];
    }
}
