<?php
/**
 * WebSocket心跳测试脚本
 * 测试修复后的心跳更新功能
 */

require_once 'php/api/vendor/autoload.php';

use Ratchet\Client\WebSocket;
use Ratchet\Client\Connector;

// WebSocket连接信息
$sessionId = 'ws_Qg2gTJJOPhCgA3PEvpcaKWAA9SBCfG9N';
$websocketUrl = "wss://api.tiptop.cn:8080?session_id={$sessionId}";

echo "🔗 开始测试WebSocket心跳更新功能...\n";
echo "📍 Session ID: {$sessionId}\n";
echo "🌐 WebSocket URL: {$websocketUrl}\n\n";

// 检查初始心跳时间
function checkHeartbeat($sessionId) {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ai_tool', 'root', 'rootroot');
    $stmt = $pdo->prepare("SELECT last_ping_at, TIMESTAMPDIFF(SECOND, last_ping_at, NOW()) as seconds_since_ping FROM p_websocket_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "💓 心跳时间: {$result['last_ping_at']} (距离现在 {$result['seconds_since_ping']} 秒)\n";
        return $result;
    } else {
        echo "❌ 未找到WebSocket session\n";
        return null;
    }
}

echo "📊 检查初始心跳时间:\n";
$initialHeartbeat = checkHeartbeat($sessionId);

// 创建WebSocket连接
$connector = new Connector();

try {
    echo "\n🔌 尝试连接WebSocket服务器...\n";
    
    $connector($websocketUrl)
        ->then(function (WebSocket $conn) use ($sessionId) {
            echo "✅ WebSocket连接成功建立!\n";
            
            // 发送心跳消息
            $heartbeatMessage = json_encode([
                'type' => 'heartbeat',
                'session_id' => $sessionId,
                'timestamp' => time()
            ]);
            
            echo "💓 发送心跳消息: {$heartbeatMessage}\n";
            $conn->send($heartbeatMessage);
            
            // 等待几秒钟让服务器处理心跳
            sleep(3);
            
            // 检查心跳时间是否更新
            echo "\n📊 检查心跳时间是否更新:\n";
            checkHeartbeat($sessionId);
            
            // 关闭连接
            $conn->close();
            echo "\n🔚 连接已关闭\n";
            
        }, function (\Exception $e) {
            echo "❌ WebSocket连接失败: " . $e->getMessage() . "\n";
            
            // 即使连接失败，也检查一下心跳时间
            echo "\n📊 连接失败后检查心跳时间:\n";
            checkHeartbeat($sessionId);
        });
        
} catch (Exception $e) {
    echo "❌ 发生异常: " . $e->getMessage() . "\n";
}

echo "\n✨ 测试完成!\n";
