USE ai_tool;

INSERT INTO p_projects (
    user_id,
    title,
    description,
    style_id,
    story_content,
    ai_generated_title,
    title_confirmed,
    status,
    project_config,
    metadata,
    last_accessed_at,
    view_count,
    is_public,
    created_at,
    updated_at
) VALUES (
    1,
    'Test Project for WebSocket',
    'A test project for WebSocket text generation',
    1,
    'This is a test story content',
    'Test Project for WebSocket',
    1,
    'draft',
    '{}',
    '{}',
    NOW(),
    0,
    0,
    NOW(),
    NOW()
);

SELECT id, title, user_id, status FROM p_projects WHERE title LIKE '%Test Project%';
