<?php

namespace App\Http\Controllers\PyApi;

use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Helpers\LogCheckHelper;
use Carbon\Carbon;

/**
 * 事件发布控制器
 * 处理系统内部事件的发布和管理
 */
class EventController extends Controller
{
    /**
     * 发布事件
     * @ApiTitle(发布系统事件)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/events/publish)
     * @ApiParams(name="event_type", type="string", required=true, description="事件类型")
     * @ApiParams(name="business_id", type="string", required=true, description="业务ID")
     * @ApiParams(name="user_id", type="int", required=true, description="用户ID")
     * @ApiParams(name="error_details", type="object", required=false, description="错误详情")
     * @ApiParams(name="metadata", type="object", required=false, description="元数据")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "事件发布成功",
     *   "data": {
     *     "event_id": "evt_123456",
     *     "event_type": "storyboard_generation_failed",
     *     "published_at": "2024-01-01T12:00:00Z"
     *   }
     * })
     */
    public function publish(Request $request)
    {
        try {
            $rules = [
                'event_type' => 'required|string|max:100',
                'business_id' => 'required|string|max:100',
                'user_id' => 'required|integer',
                'error_details' => 'sometimes|array',
                'metadata' => 'sometimes|array'
            ];

            $this->validateData($request->all(), $rules, [], []);

            // 使用AuthService进行认证（内部服务调用可能需要特殊Token）
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $eventType = $request->get('event_type');
            $businessId = $request->get('business_id');
            $userId = $request->get('user_id');
            $errorDetails = $request->get('error_details', []);
            $metadata = $request->get('metadata', []);

            // 验证事件类型
            $allowedEventTypes = [
                'storyboard_generation_failed',
                'storyboard_generation_completed',
                'image_generation_failed',
                'image_generation_completed',
                'ai_service_error',
                'points_transaction_failed',
                'websocket_connection_error'
            ];

            if (!in_array($eventType, $allowedEventTypes)) {
                return $this->errorResponse(
                    ApiCodeEnum::VALIDATION_ERROR,
                    '不支持的事件类型',
                    ['allowed_types' => $allowedEventTypes]
                );
            }

            // 生成事件ID
            $eventId = 'evt_' . time() . '_' . substr(md5($businessId . $eventType), 0, 8);

            // 构建事件数据
            $eventData = [
                'event_id' => $eventId,
                'event_type' => $eventType,
                'business_id' => $businessId,
                'user_id' => $userId,
                'error_details' => $errorDetails,
                'metadata' => $metadata,
                'published_at' => Carbon::now()->format('c'),
                'source' => 'api_service',
                'version' => '1.0'
            ];

            // 写入事件日志到数据库
            $this->writeEventLog($eventData);

            // 发布到事件总线（异步处理）
            $this->publishToEventBus($eventData);

            // 根据事件类型执行特定处理
            $this->handleSpecificEvent($eventType, $eventData);

            Log::info('事件发布成功', [
                'event_id' => $eventId,
                'event_type' => $eventType,
                'business_id' => $businessId,
                'user_id' => $userId
            ]);

            return $this->successResponse([
                'event_id' => $eventId,
                'event_type' => $eventType,
                'business_id' => $businessId,
                'published_at' => $eventData['published_at']
            ], '事件发布成功');

        } catch (\Exception $e) {
            Log::error('事件发布失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '事件发布失败', []);
        }
    }

    /**
     * 获取事件列表
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/events)
     * @ApiParams(name="event_type", type="string", required=false, description="事件类型筛选")
     * @ApiParams(name="business_id", type="string", required=false, description="业务ID筛选")
     * @ApiParams(name="user_id", type="int", required=false, description="用户ID筛选")
     * @ApiParams(name="limit", type="int", required=false, description="返回数量限制，默认20")
     */
    public function list(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $eventType = $request->get('event_type');
            $businessId = $request->get('business_id');
            $userId = $request->get('user_id');
            $limit = min($request->get('limit', 20), 100); // 最大100条

            $query = DB::table('system_events')
                ->orderBy('created_at', 'desc')
                ->limit($limit);

            if ($eventType) {
                $query->where('event_type', $eventType);
            }

            if ($businessId) {
                $query->where('business_id', $businessId);
            }

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $events = $query->get();

            return $this->successResponse([
                'events' => $events,
                'total' => $events->count(),
                'filters' => [
                    'event_type' => $eventType,
                    'business_id' => $businessId,
                    'user_id' => $userId
                ]
            ], '获取事件列表成功');

        } catch (\Exception $e) {
            Log::error('获取事件列表失败', [
                'method' => __METHOD__,
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取事件列表失败', []);
        }
    }

    /**
     * 写入事件日志到数据库
     */
    private function writeEventLog(array $eventData): void
    {
        try {
            DB::table('system_events')->insert([
                'event_id' => $eventData['event_id'],
                'event_type' => $eventData['event_type'],
                'business_id' => $eventData['business_id'],
                'user_id' => $eventData['user_id'],
                'error_details' => json_encode($eventData['error_details']),
                'metadata' => json_encode($eventData['metadata']),
                'source' => $eventData['source'],
                'version' => $eventData['version'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
        } catch (\Exception $e) {
            Log::error('写入事件日志失败', [
                'event_id' => $eventData['event_id'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发布到事件总线
     */
    private function publishToEventBus(array $eventData): void
    {
        try {
            // 这里可以集成Redis、RabbitMQ等消息队列
            // 暂时使用日志记录
            Log::info('事件总线发布', [
                'event_id' => $eventData['event_id'],
                'event_type' => $eventData['event_type'],
                'business_id' => $eventData['business_id']
            ]);
        } catch (\Exception $e) {
            Log::error('事件总线发布失败', [
                'event_id' => $eventData['event_id'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理特定事件类型
     */
    private function handleSpecificEvent(string $eventType, array $eventData): void
    {
        switch ($eventType) {
            case 'storyboard_generation_failed':
                $this->handleStoryboardGenerationFailed($eventData);
                break;
            
            case 'ai_service_error':
                $this->handleAiServiceError($eventData);
                break;
            
            default:
                // 默认处理
                break;
        }
    }

    /**
     * 处理分镜生成失败事件
     */
    private function handleStoryboardGenerationFailed(array $eventData): void
    {
        // 可以在这里添加特定的失败处理逻辑
        // 例如：通知管理员、更新统计数据等
        Log::info('处理分镜生成失败事件', [
            'event_id' => $eventData['event_id'],
            'business_id' => $eventData['business_id']
        ]);
    }

    /**
     * 处理AI服务错误事件
     */
    private function handleAiServiceError(array $eventData): void
    {
        // 可以在这里添加AI服务错误的特定处理逻辑
        Log::info('处理AI服务错误事件', [
            'event_id' => $eventData['event_id'],
            'error_details' => $eventData['error_details']
        ]);
    }
}
