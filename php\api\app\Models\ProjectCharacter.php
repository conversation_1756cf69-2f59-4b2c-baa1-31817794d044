<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 项目角色模型
 * 存储从故事中提取的角色信息
 * 
 * @property int $id
 * @property int $project_id
 * @property string $name
 * @property string $description
 * @property string $role_type
 * @property string $importance
 * @property string $binding_status
 * @property int $character_library_id
 * @property array $extracted_info
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class ProjectCharacter extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'project_characters';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'project_id',
        'name',
        'description',
        'role_type',
        'importance',
        'binding_status',
        'character_library_id',
        'extracted_info'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'extracted_info' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'role_type' => 'supporting',
        'importance' => 'medium',
        'binding_status' => 'unbound'
    ];

    /**
     * 角色类型常量
     */
    const ROLE_PROTAGONIST = 'protagonist';
    const ROLE_ANTAGONIST = 'antagonist';
    const ROLE_SUPPORTING = 'supporting';
    const ROLE_MINOR = 'minor';

    /**
     * 重要性常量
     */
    const IMPORTANCE_HIGH = 'high';
    const IMPORTANCE_MEDIUM = 'medium';
    const IMPORTANCE_LOW = 'low';

    /**
     * 绑定状态常量
     */
    const STATUS_UNBOUND = 'unbound';
    const STATUS_BOUND = 'bound';
    const STATUS_GENERATING = 'generating';

    /**
     * 关联项目
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * 关联角色库中的角色
     */
    public function characterLibrary(): BelongsTo
    {
        return $this->belongsTo(CharacterLibrary::class, 'character_library_id');
    }

    /**
     * 关联分镜角色
     */
    public function storyboardCharacters(): HasMany
    {
        return $this->hasMany(StoryboardCharacter::class, 'project_character_id');
    }

    /**
     * 查询作用域：按项目筛选
     */
    public function scopeByProject($query, int $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * 查询作用域：按绑定状态筛选
     */
    public function scopeByBindingStatus($query, string $status)
    {
        return $query->where('binding_status', $status);
    }

    /**
     * 查询作用域：按角色类型筛选
     */
    public function scopeByRoleType($query, string $roleType)
    {
        return $query->where('role_type', $roleType);
    }

    /**
     * 查询作用域：按重要性筛选
     */
    public function scopeByImportance($query, string $importance)
    {
        return $query->where('importance', $importance);
    }

    /**
     * 查询作用域：未绑定的角色
     */
    public function scopeUnbound($query)
    {
        return $query->where('binding_status', self::STATUS_UNBOUND);
    }

    /**
     * 查询作用域：已绑定的角色
     */
    public function scopeBound($query)
    {
        return $query->where('binding_status', self::STATUS_BOUND);
    }

    /**
     * 查询作用域：主要角色（主角和反派）
     */
    public function scopeMainCharacters($query)
    {
        return $query->whereIn('role_type', [self::ROLE_PROTAGONIST, self::ROLE_ANTAGONIST]);
    }

    /**
     * 查询作用域：高重要性角色
     */
    public function scopeHighImportance($query)
    {
        return $query->where('importance', self::IMPORTANCE_HIGH);
    }

    /**
     * 绑定角色库中的角色
     */
    public function bindCharacter(int $characterLibraryId): void
    {
        $this->character_library_id = $characterLibraryId;
        $this->binding_status = self::STATUS_BOUND;
        $this->save();
    }

    /**
     * 解除绑定
     */
    public function unbind(): void
    {
        $this->character_library_id = null;
        $this->binding_status = self::STATUS_UNBOUND;
        $this->save();
    }

    /**
     * 标记为生成中
     */
    public function markAsGenerating(): void
    {
        $this->binding_status = self::STATUS_GENERATING;
        $this->save();
    }

    /**
     * 检查是否已绑定
     */
    public function isBound(): bool
    {
        return $this->binding_status === self::STATUS_BOUND;
    }

    /**
     * 检查是否未绑定
     */
    public function isUnbound(): bool
    {
        return $this->binding_status === self::STATUS_UNBOUND;
    }

    /**
     * 检查是否为主要角色
     */
    public function isMainCharacter(): bool
    {
        return in_array($this->role_type, [self::ROLE_PROTAGONIST, self::ROLE_ANTAGONIST]);
    }

    /**
     * 检查是否为高重要性角色
     */
    public function isHighImportance(): bool
    {
        return $this->importance === self::IMPORTANCE_HIGH;
    }

    /**
     * 获取角色类型的中文名称
     */
    public function getRoleTypeName(): string
    {
        $names = [
            self::ROLE_PROTAGONIST => '主角',
            self::ROLE_ANTAGONIST => '反派',
            self::ROLE_SUPPORTING => '配角',
            self::ROLE_MINOR => '次要角色'
        ];

        return $names[$this->role_type] ?? '未知';
    }

    /**
     * 获取重要性的中文名称
     */
    public function getImportanceName(): string
    {
        $names = [
            self::IMPORTANCE_HIGH => '高',
            self::IMPORTANCE_MEDIUM => '中',
            self::IMPORTANCE_LOW => '低'
        ];

        return $names[$this->importance] ?? '未知';
    }

    /**
     * 获取绑定状态的中文名称
     */
    public function getBindingStatusName(): string
    {
        $names = [
            self::STATUS_UNBOUND => '未绑定',
            self::STATUS_BOUND => '已绑定',
            self::STATUS_GENERATING => '生成中'
        ];

        return $names[$this->binding_status] ?? '未知';
    }

    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'name' => $this->name,
            'description' => $this->description,
            'role_type' => $this->role_type,
            'role_type_name' => $this->getRoleTypeName(),
            'importance' => $this->importance,
            'importance_name' => $this->getImportanceName(),
            'binding_status' => $this->binding_status,
            'binding_status_name' => $this->getBindingStatusName(),
            'character_library_id' => $this->character_library_id,
            'extracted_info' => $this->extracted_info,
            'created_at' => $this->created_at?->format('c'),
            'updated_at' => $this->updated_at?->format('c'),
            'character_library' => $this->characterLibrary?->toApiArray(),
        ];
    }
}
