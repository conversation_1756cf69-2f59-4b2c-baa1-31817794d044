<?php

namespace App\Jobs;

use App\Services\PyApi\ProjectService;
use App\Services\PyApi\StoryService;
use App\Services\PyApi\WebSocketEventService;
use App\Enums\ApiCodeEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * 异步处理项目创建任务
 * 实现图表中的完整 WebSocket 进度推送流程
 */
class ProcessProjectCreation implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $taskId;
    protected $userId;
    protected $projectData;
    protected $mode;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300; // 5分钟

    /**
     * 最大重试次数
     */
    public $tries = 3;

    public function __construct(string $taskId, int $userId, array $projectData, string $mode = 'intelligent')
    {
        $this->taskId = $taskId;
        $this->userId = $userId;
        $this->projectData = $projectData;
        $this->mode = $mode;
    }

    /**
     * 执行任务
     */
    public function handle()
    {
        $webSocketEventService = app(WebSocketEventService::class);
        $projectService = app(ProjectService::class);
        $storyService = app(StoryService::class);

        try {
            DB::beginTransaction();

            // 推送任务开始进度
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                10,
                "开始项目创建任务"
            );

            if ($this->mode === 'intelligent') {
                // 智能故事生成模式
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    20,
                    "开始故事生成"
                );

                // 生成故事
                $storyResult = $storyService->generateStory(
                    $this->userId,
                    $this->projectData['story_prompt'],
                    $this->projectData['style_id'] ?? null,
                    null, // project_id 暂时为null
                    $this->projectData['generation_params'] ?? []
                );

                if ($storyResult['code'] !== ApiCodeEnum::SUCCESS) {
                    throw new \Exception('故事生成失败：' . $storyResult['message']);
                }

                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    50,
                    "故事生成完成，开始分镜拆解"
                );

                // 使用生成的故事内容
                $storyContent = $storyResult['data']['generated_text'] ?? $this->projectData['story_prompt'];

            } else {
                // 自有故事模式
                $storyContent = $this->projectData['story_content'];
                
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    30,
                    "开始故事分镜拆解"
                );
            }

            // 推送进度更新
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                70,
                "创建项目记录"
            );

            // 创建项目
            $result = $projectService->createWithStory(
                $this->userId,
                $this->projectData['style_id'] ?? null,
                $storyContent,
                $this->projectData['title'],
                $this->projectData['description'] ?? ''
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 创建成功
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    90,
                    "保存项目数据"
                );

                // 推送完成进度
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    100,
                    "项目创建完成"
                );

                // 推送任务完成事件
                $webSocketEventService->pushAiGenerationCompleted(
                    $this->taskId,
                    $this->userId
                );

                DB::commit();

                Log::info('项目创建任务完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'mode' => $this->mode,
                    'project_id' => $result['data']['id'] ?? null
                ]);

            } else {
                // 创建失败
                throw new \Exception($result['message']);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            // 推送任务失败事件
            $webSocketEventService->pushAiGenerationFailed(
                $this->taskId,
                $this->userId,
                $e->getMessage()
            );

            // 发布失败事件到事件总线
            $this->publishFailureEvent($e->getMessage());

            Log::error('项目创建任务失败', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'mode' => $this->mode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 任务失败处理
     */
    public function failed(\Throwable $exception)
    {
        $webSocketEventService = app(WebSocketEventService::class);

        // 推送任务失败事件
        $webSocketEventService->pushAiGenerationFailed(
            $this->taskId,
            $this->userId,
            $exception->getMessage()
        );

        // 发布最终失败事件
        $this->publishFailureEvent($exception->getMessage());

        Log::error('项目创建任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'mode' => $this->mode,
            'error' => $exception->getMessage()
        ]);
    }

    /**
     * 发布失败事件到事件总线
     */
    private function publishFailureEvent(string $errorMessage): void
    {
        try {
            // 调用事件发布API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('app.internal_api_token', 'internal_service_token'),
                'Content-Type' => 'application/json'
            ])->post(config('app.url') . '/py-api/events/publish', [
                'event_type' => 'project_creation_failed',
                'business_id' => $this->taskId,
                'user_id' => $this->userId,
                'error_details' => [
                    'error_message' => $errorMessage,
                    'task_id' => $this->taskId,
                    'mode' => $this->mode,
                    'creation_type' => $this->mode
                ],
                'metadata' => [
                    'project_data' => $this->projectData,
                    'mode' => $this->mode,
                    'failed_at' => now()->format('c')
                ]
            ]);

            if ($response->successful()) {
                Log::info('项目创建失败事件发布成功', [
                    'task_id' => $this->taskId,
                    'event_response' => $response->json()
                ]);
            } else {
                Log::warning('项目创建失败事件发布失败', [
                    'task_id' => $this->taskId,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('发布项目创建失败事件异常', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
