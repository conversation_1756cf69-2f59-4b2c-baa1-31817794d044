<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Carbon\Carbon;

// 创建数据库连接
$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'port' => '3306',
    'database' => 'ai_tool',
    'username' => 'root',
    'password' => 'rootroot',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => 'p_',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // 插入测试项目数据
    $projectId = Capsule::table('projects')->insertGetId([
        'user_id' => 1,
        'title' => 'Test Project for WebSocket',
        'description' => 'A test project for WebSocket text generation',
        'style_id' => 1,
        'story_content' => 'This is a test story content for WebSocket testing',
        'ai_generated_title' => 'Test Project for WebSocket',
        'title_confirmed' => 1,
        'status' => 'draft',
        'project_config' => json_encode(['type' => 'text_generation']),
        'metadata' => json_encode(['created_for' => 'websocket_test']),
        'last_accessed_at' => Carbon::now(),
        'view_count' => 0,
        'is_public' => 0,
        'created_at' => Carbon::now(),
        'updated_at' => Carbon::now(),
    ]);

    echo "✅ 测试项目创建成功！\n";
    echo "项目ID: {$projectId}\n";
    
    // 查询插入的数据
    $project = Capsule::table('projects')->where('id', $projectId)->first();
    echo "项目标题: {$project->title}\n";
    echo "项目状态: {$project->status}\n";
    echo "用户ID: {$project->user_id}\n";
    
} catch (Exception $e) {
    echo "❌ 插入数据失败: " . $e->getMessage() . "\n";
}
